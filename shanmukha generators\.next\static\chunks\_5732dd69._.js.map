{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/shanmukha%20generators/src/utils/fileUpload.ts"], "sourcesContent": ["// File upload utilities and validation\n\nexport interface UploadedFile {\n  file: File;\n  id: string;\n  preview: string;\n  progress: number;\n  status: 'pending' | 'uploading' | 'success' | 'error';\n  error?: string;\n  url?: string;\n}\n\nexport interface FileValidationResult {\n  isValid: boolean;\n  error?: string;\n}\n\n// Supported file types\nexport const SUPPORTED_FILE_TYPES = {\n  'image/jpeg': ['.jpg', '.jpeg'],\n  'image/png': ['.png'],\n  'image/gif': ['.gif'],\n  'image/webp': ['.webp']\n};\n\n// File size limits\nexport const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB per file\nexport const MAX_TOTAL_SIZE = 25 * 1024 * 1024; // 25MB total\nexport const MAX_FILES = 10;\n\n/**\n * Validate a single file\n */\nexport function validateFile(file: File): FileValidationResult {\n  // Check file type\n  if (!Object.keys(SUPPORTED_FILE_TYPES).includes(file.type)) {\n    return {\n      isValid: false,\n      error: `Unsupported file type. Please use: ${Object.values(SUPPORTED_FILE_TYPES).flat().join(', ')}`\n    };\n  }\n\n  // Check file size\n  if (file.size > MAX_FILE_SIZE) {\n    return {\n      isValid: false,\n      error: `File size too large. Maximum size is ${formatFileSize(MAX_FILE_SIZE)}`\n    };\n  }\n\n  // Check file extension matches MIME type\n  const allowedExtensions = SUPPORTED_FILE_TYPES[file.type as keyof typeof SUPPORTED_FILE_TYPES];\n  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\n  \n  if (!allowedExtensions.includes(fileExtension)) {\n    return {\n      isValid: false,\n      error: 'File extension does not match file type'\n    };\n  }\n\n  return { isValid: true };\n}\n\n/**\n * Validate multiple files\n */\nexport function validateFiles(files: File[]): FileValidationResult {\n  // Check number of files\n  if (files.length > MAX_FILES) {\n    return {\n      isValid: false,\n      error: `Too many files. Maximum is ${MAX_FILES} files`\n    };\n  }\n\n  // Check total size\n  const totalSize = files.reduce((sum, file) => sum + file.size, 0);\n  if (totalSize > MAX_TOTAL_SIZE) {\n    return {\n      isValid: false,\n      error: `Total file size too large. Maximum is ${formatFileSize(MAX_TOTAL_SIZE)}`\n    };\n  }\n\n  // Validate each file\n  for (const file of files) {\n    const validation = validateFile(file);\n    if (!validation.isValid) {\n      return validation;\n    }\n  }\n\n  return { isValid: true };\n}\n\n/**\n * Create a preview URL for an image file\n */\nexport function createFilePreview(file: File): string {\n  return URL.createObjectURL(file);\n}\n\n/**\n * Generate a unique file ID\n */\nexport function generateFileId(): string {\n  return `file_${Date.now()}_${Math.random().toString(36).substring(7)}`;\n}\n\n/**\n * Format file size for display\n */\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\n/**\n * Upload a single file to the server\n */\nexport async function uploadFile(file: File, onProgress?: (progress: number) => void): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    const xhr = new XMLHttpRequest();\n\n    // Track upload progress\n    xhr.upload.addEventListener('progress', (event) => {\n      if (event.lengthComputable && onProgress) {\n        const progress = Math.round((event.loaded / event.total) * 100);\n        onProgress(progress);\n      }\n    });\n\n    xhr.addEventListener('load', () => {\n      if (xhr.status === 200) {\n        try {\n          const response = JSON.parse(xhr.responseText);\n          if (response.success) {\n            resolve(response.data.url);\n          } else {\n            reject(new Error(response.error || 'Upload failed'));\n          }\n        } catch (error) {\n          reject(new Error('Invalid response from server'));\n        }\n      } else {\n        reject(new Error(`Upload failed with status ${xhr.status}`));\n      }\n    });\n\n    xhr.addEventListener('error', () => {\n      reject(new Error('Network error during upload'));\n    });\n\n    xhr.addEventListener('abort', () => {\n      reject(new Error('Upload was cancelled'));\n    });\n\n    xhr.open('POST', '/api/admin/upload');\n    xhr.send(formData);\n  });\n}\n\n/**\n * Upload multiple files with progress tracking\n */\nexport async function uploadFiles(\n  files: UploadedFile[],\n  onProgress: (fileId: string, progress: number) => void,\n  onComplete: (fileId: string, url: string) => void,\n  onError: (fileId: string, error: string) => void\n): Promise<void> {\n  const uploadPromises = files.map(async (uploadedFile) => {\n    try {\n      const url = await uploadFile(uploadedFile.file, (progress) => {\n        onProgress(uploadedFile.id, progress);\n      });\n      onComplete(uploadedFile.id, url);\n    } catch (error) {\n      onError(uploadedFile.id, error instanceof Error ? error.message : 'Upload failed');\n    }\n  });\n\n  await Promise.all(uploadPromises);\n}\n\n/**\n * Clean up preview URLs to prevent memory leaks\n */\nexport function cleanupPreviews(files: UploadedFile[]): void {\n  files.forEach(file => {\n    if (file.preview) {\n      URL.revokeObjectURL(file.preview);\n    }\n  });\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;;;;;;;;;;;;AAkBhC,MAAM,uBAAuB;IAClC,cAAc;QAAC;QAAQ;KAAQ;IAC/B,aAAa;QAAC;KAAO;IACrB,aAAa;QAAC;KAAO;IACrB,cAAc;QAAC;KAAQ;AACzB;AAGO,MAAM,gBAAgB,IAAI,OAAO,MAAM,eAAe;AACtD,MAAM,iBAAiB,KAAK,OAAO,MAAM,aAAa;AACtD,MAAM,YAAY;AAKlB,SAAS,aAAa,IAAU;IACrC,kBAAkB;IAClB,IAAI,CAAC,OAAO,IAAI,CAAC,sBAAsB,QAAQ,CAAC,KAAK,IAAI,GAAG;QAC1D,OAAO;YACL,SAAS;YACT,OAAO,CAAC,mCAAmC,EAAE,OAAO,MAAM,CAAC,sBAAsB,IAAI,GAAG,IAAI,CAAC,OAAO;QACtG;IACF;IAEA,kBAAkB;IAClB,IAAI,KAAK,IAAI,GAAG,eAAe;QAC7B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,qCAAqC,EAAE,eAAe,gBAAgB;QAChF;IACF;IAEA,yCAAyC;IACzC,MAAM,oBAAoB,oBAAoB,CAAC,KAAK,IAAI,CAAsC;IAC9F,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;IAExD,IAAI,CAAC,kBAAkB,QAAQ,CAAC,gBAAgB;QAC9C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,SAAS,cAAc,KAAa;IACzC,wBAAwB;IACxB,IAAI,MAAM,MAAM,GAAG,WAAW;QAC5B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,2BAA2B,EAAE,UAAU,MAAM,CAAC;QACxD;IACF;IAEA,mBAAmB;IACnB,MAAM,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,IAAI,EAAE;IAC/D,IAAI,YAAY,gBAAgB;QAC9B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,sCAAsC,EAAE,eAAe,iBAAiB;QAClF;IACF;IAEA,qBAAqB;IACrB,KAAK,MAAM,QAAQ,MAAO;QACxB,MAAM,aAAa,aAAa;QAChC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO;QACT;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,SAAS,kBAAkB,IAAU;IAC1C,OAAO,IAAI,eAAe,CAAC;AAC7B;AAKO,SAAS;IACd,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,IAAI;AACxE;AAKO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAKO,eAAe,WAAW,IAAU,EAAE,UAAuC;IAClF,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,MAAM,MAAM,IAAI;QAEhB,wBAAwB;QACxB,IAAI,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC;YACvC,IAAI,MAAM,gBAAgB,IAAI,YAAY;gBACxC,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;gBAC3D,WAAW;YACb;QACF;QAEA,IAAI,gBAAgB,CAAC,QAAQ;YAC3B,IAAI,IAAI,MAAM,KAAK,KAAK;gBACtB,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;oBAC5C,IAAI,SAAS,OAAO,EAAE;wBACpB,QAAQ,SAAS,IAAI,CAAC,GAAG;oBAC3B,OAAO;wBACL,OAAO,IAAI,MAAM,SAAS,KAAK,IAAI;oBACrC;gBACF,EAAE,OAAO,OAAO;oBACd,OAAO,IAAI,MAAM;gBACnB;YACF,OAAO;gBACL,OAAO,IAAI,MAAM,CAAC,0BAA0B,EAAE,IAAI,MAAM,EAAE;YAC5D;QACF;QAEA,IAAI,gBAAgB,CAAC,SAAS;YAC5B,OAAO,IAAI,MAAM;QACnB;QAEA,IAAI,gBAAgB,CAAC,SAAS;YAC5B,OAAO,IAAI,MAAM;QACnB;QAEA,IAAI,IAAI,CAAC,QAAQ;QACjB,IAAI,IAAI,CAAC;IACX;AACF;AAKO,eAAe,YACpB,KAAqB,EACrB,UAAsD,EACtD,UAAiD,EACjD,OAAgD;IAEhD,MAAM,iBAAiB,MAAM,GAAG,CAAC,OAAO;QACtC,IAAI;YACF,MAAM,MAAM,MAAM,WAAW,aAAa,IAAI,EAAE,CAAC;gBAC/C,WAAW,aAAa,EAAE,EAAE;YAC9B;YACA,WAAW,aAAa,EAAE,EAAE;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,aAAa,EAAE,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpE;IACF;IAEA,MAAM,QAAQ,GAAG,CAAC;AACpB;AAKO,SAAS,gBAAgB,KAAqB;IACnD,MAAM,OAAO,CAAC,CAAA;QACZ,IAAI,KAAK,OAAO,EAAE;YAChB,IAAI,eAAe,CAAC,KAAK,OAAO;QAClC;IACF;AACF", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/shanmukha%20generators/src/app/admin/add-generator/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { ArrowLeftIcon, PhotoIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport FileUpload from '@/components/FileUpload';\nimport { UploadedFile, uploadFiles } from '@/utils/fileUpload';\n\nexport default function AddGenerator() {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const router = useRouter();\n\n  const [formData, setFormData] = useState({\n    brand: '',\n    model: '',\n    price: '',\n    hours_run: '',\n    location_text: '',\n    description: '',\n    seller_whatsapp_id: '',\n    seller_display_name: '',\n    images: [] as string[]\n  });\n\n  const [imageUrls, setImageUrls] = useState<string[]>(['']);\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [imageMode, setImageMode] = useState<'upload' | 'url'>('upload');\n  const [isUploading, setIsUploading] = useState(false);\n\n  useEffect(() => {\n    checkAuth();\n  }, []);\n\n  const checkAuth = async () => {\n    try {\n      const response = await fetch('/api/admin/auth');\n      const data = await response.json();\n      \n      if (!data.success) {\n        router.push('/admin/login');\n      }\n    } catch (error) {\n      router.push('/admin/login');\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleImageUrlChange = (index: number, value: string) => {\n    const newImageUrls = [...imageUrls];\n    newImageUrls[index] = value;\n    setImageUrls(newImageUrls);\n    \n    // Update form data with non-empty URLs\n    setFormData(prev => ({\n      ...prev,\n      images: newImageUrls.filter(url => url.trim() !== '')\n    }));\n  };\n\n  const addImageUrlField = () => {\n    setImageUrls([...imageUrls, '']);\n  };\n\n  const removeImageUrlField = (index: number) => {\n    const newImageUrls = imageUrls.filter((_, i) => i !== index);\n    setImageUrls(newImageUrls);\n    setFormData(prev => ({\n      ...prev,\n      images: newImageUrls.filter(url => url.trim() !== '')\n    }));\n  };\n\n  const handleFilesChange = (files: UploadedFile[]) => {\n    setUploadedFiles(files);\n  };\n\n  const handleFileUpload = async () => {\n    if (uploadedFiles.length === 0) return [];\n\n    setIsUploading(true);\n    const uploadedUrls: string[] = [];\n\n    try {\n      await uploadFiles(\n        uploadedFiles.filter(f => f.status === 'pending'),\n        (fileId, progress) => {\n          setUploadedFiles(prev =>\n            prev.map(file =>\n              file.id === fileId ? { ...file, progress, status: 'uploading' } : file\n            )\n          );\n        },\n        (fileId, url) => {\n          setUploadedFiles(prev =>\n            prev.map(file =>\n              file.id === fileId ? { ...file, url, status: 'success' } : file\n            )\n          );\n          uploadedUrls.push(url);\n        },\n        (fileId, error) => {\n          setUploadedFiles(prev =>\n            prev.map(file =>\n              file.id === fileId ? { ...file, error, status: 'error' } : file\n            )\n          );\n        }\n      );\n\n      return uploadedUrls;\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      // Validate required fields\n      if (!formData.brand || !formData.model || !formData.price || !formData.hours_run ||\n          !formData.location_text || !formData.description || !formData.seller_whatsapp_id) {\n        throw new Error('Please fill in all required fields');\n      }\n\n      // Validate price and hours are numbers\n      const price = parseInt(formData.price);\n      const hours = parseInt(formData.hours_run);\n\n      if (isNaN(price) || price <= 0) {\n        throw new Error('Please enter a valid price');\n      }\n\n      if (isNaN(hours) || hours < 0) {\n        throw new Error('Please enter valid running hours');\n      }\n\n      // Validate WhatsApp ID format\n      if (!/^\\d{10,15}$/.test(formData.seller_whatsapp_id)) {\n        throw new Error('Please enter a valid WhatsApp ID (10-15 digits)');\n      }\n\n      // Check if there are pending uploads\n      const hasPendingUploads = uploadedFiles.some(f => f.status === 'pending' || f.status === 'uploading');\n      if (hasPendingUploads) {\n        throw new Error('Please wait for all file uploads to complete');\n      }\n\n      // Collect all image URLs\n      let allImageUrls: string[] = [];\n\n      // Add uploaded file URLs\n      const successfulUploads = uploadedFiles.filter(f => f.status === 'success' && f.url);\n      allImageUrls = [...allImageUrls, ...successfulUploads.map(f => f.url!)];\n\n      // Add manual URLs (if using URL mode)\n      if (imageMode === 'url') {\n        const validUrls = formData.images.filter(url => url.trim() !== '');\n        allImageUrls = [...allImageUrls, ...validUrls];\n      }\n\n      const response = await fetch('/api/admin/generators/manual', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          brand: formData.brand.trim(),\n          model: formData.model.trim(),\n          price: price,\n          hours_run: hours,\n          location_text: formData.location_text.trim(),\n          description: formData.description.trim(),\n          seller_whatsapp_id: formData.seller_whatsapp_id.trim(),\n          seller_display_name: formData.seller_display_name.trim() || undefined,\n          images: allImageUrls.map(url => ({ url: url.trim() }))\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setSuccess('Generator added successfully!');\n        // Reset form\n        setFormData({\n          brand: '',\n          model: '',\n          price: '',\n          hours_run: '',\n          location_text: '',\n          description: '',\n          seller_whatsapp_id: '',\n          seller_display_name: '',\n          images: []\n        });\n        setImageUrls(['']);\n        setUploadedFiles([]);\n        setImageMode('upload');\n        \n        // Redirect to dashboard after 2 seconds\n        setTimeout(() => {\n          router.push('/admin/dashboard');\n        }, 2000);\n      } else {\n        setError(data.error || 'Failed to add generator');\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'An error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => router.push('/admin/dashboard')}\n                className=\"text-gray-600 hover:text-gray-900\"\n              >\n                <ArrowLeftIcon className=\"h-6 w-6\" />\n              </button>\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900\">Add Generator Manually</h1>\n                <p className=\"text-gray-600\">Create a new generator listing</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Generator Information */}\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Generator Information</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Brand *\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"brand\"\n                    value={formData.brand}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"e.g., Kirloskar, Mahindra, Cummins\"\n                    required\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Model *\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"model\"\n                    value={formData.model}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"e.g., KG1-62.5AS, MDG-125\"\n                    required\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Price (₹) *\n                  </label>\n                  <input\n                    type=\"number\"\n                    name=\"price\"\n                    value={formData.price}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"850000\"\n                    min=\"0\"\n                    required\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Running Hours *\n                  </label>\n                  <input\n                    type=\"number\"\n                    name=\"hours_run\"\n                    value={formData.hours_run}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"12500\"\n                    min=\"0\"\n                    required\n                  />\n                </div>\n              </div>\n              \n              <div className=\"mt-4\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Location *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"location_text\"\n                  value={formData.location_text}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"Mumbai, Maharashtra\"\n                  required\n                />\n              </div>\n              \n              <div className=\"mt-4\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Description *\n                </label>\n                <textarea\n                  name=\"description\"\n                  value={formData.description}\n                  onChange={handleInputChange}\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"Excellent condition diesel generator, well maintained with all documents. Recently serviced and ready for immediate use.\"\n                  required\n                />\n              </div>\n            </div>\n\n            {/* Seller Information */}\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Seller Information</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    WhatsApp ID *\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"seller_whatsapp_id\"\n                    value={formData.seller_whatsapp_id}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"919876543210\"\n                    pattern=\"\\d{10,15}\"\n                    required\n                  />\n                  <p className=\"text-xs text-gray-500 mt-1\">10-15 digits, no spaces or symbols</p>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Display Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"seller_display_name\"\n                    value={formData.seller_display_name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"John Seller (optional)\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Images */}\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Images</h3>\n              <div className=\"space-y-3\">\n                {imageUrls.map((url, index) => (\n                  <div key={index} className=\"flex items-center space-x-2\">\n                    <PhotoIcon className=\"h-5 w-5 text-gray-400\" />\n                    <input\n                      type=\"url\"\n                      value={url}\n                      onChange={(e) => handleImageUrlChange(index, e.target.value)}\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      placeholder=\"https://example.com/image.jpg\"\n                    />\n                    {imageUrls.length > 1 && (\n                      <button\n                        type=\"button\"\n                        onClick={() => removeImageUrlField(index)}\n                        className=\"text-red-500 hover:text-red-700\"\n                      >\n                        <XMarkIcon className=\"h-5 w-5\" />\n                      </button>\n                    )}\n                  </div>\n                ))}\n                <button\n                  type=\"button\"\n                  onClick={addImageUrlField}\n                  className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\"\n                >\n                  + Add another image URL\n                </button>\n              </div>\n            </div>\n\n            {/* Error/Success Messages */}\n            {error && (\n              <div className=\"rounded-md bg-red-50 p-4\">\n                <div className=\"text-sm text-red-700\">{error}</div>\n              </div>\n            )}\n\n            {success && (\n              <div className=\"rounded-md bg-green-50 p-4\">\n                <div className=\"text-sm text-green-700\">{success}</div>\n              </div>\n            )}\n\n            {/* Submit Button */}\n            <div className=\"flex justify-end space-x-4\">\n              <button\n                type=\"button\"\n                onClick={() => router.push('/admin/dashboard')}\n                className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isLoading ? 'Adding...' : 'Add Generator'}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;;;AANA;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,OAAO;QACP,OAAO;QACP,WAAW;QACX,eAAe;QACf,aAAa;QACb,oBAAoB;QACpB,qBAAqB;QACrB,QAAQ,EAAE;IACZ;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAG;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,uBAAuB,CAAC,OAAe;QAC3C,MAAM,eAAe;eAAI;SAAU;QACnC,YAAY,CAAC,MAAM,GAAG;QACtB,aAAa;QAEb,uCAAuC;QACvC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,OAAO;YACpD,CAAC;IACH;IAEA,MAAM,mBAAmB;QACvB,aAAa;eAAI;YAAW;SAAG;IACjC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,eAAe,UAAU,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACtD,aAAa;QACb,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,OAAO;YACpD,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;IACnB;IAEA,MAAM,mBAAmB;QACvB,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,EAAE;QAEzC,eAAe;QACf,MAAM,eAAyB,EAAE;QAEjC,IAAI;YACF,MAAM,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EACd,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YACvC,CAAC,QAAQ;gBACP,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,SAAS;4BAAE,GAAG,IAAI;4BAAE;4BAAU,QAAQ;wBAAY,IAAI;YAGxE,GACA,CAAC,QAAQ;gBACP,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,SAAS;4BAAE,GAAG,IAAI;4BAAE;4BAAK,QAAQ;wBAAU,IAAI;gBAG/D,aAAa,IAAI,CAAC;YACpB,GACA,CAAC,QAAQ;gBACP,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,SAAS;4BAAE,GAAG,IAAI;4BAAE;4BAAO,QAAQ;wBAAQ,IAAI;YAGjE;YAGF,OAAO;QACT,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QACT,WAAW;QAEX,IAAI;YACF,2BAA2B;YAC3B,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,SAAS,IAC5E,CAAC,SAAS,aAAa,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,kBAAkB,EAAE;gBACpF,MAAM,IAAI,MAAM;YAClB;YAEA,uCAAuC;YACvC,MAAM,QAAQ,SAAS,SAAS,KAAK;YACrC,MAAM,QAAQ,SAAS,SAAS,SAAS;YAEzC,IAAI,MAAM,UAAU,SAAS,GAAG;gBAC9B,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,MAAM,UAAU,QAAQ,GAAG;gBAC7B,MAAM,IAAI,MAAM;YAClB;YAEA,8BAA8B;YAC9B,IAAI,CAAC,cAAc,IAAI,CAAC,SAAS,kBAAkB,GAAG;gBACpD,MAAM,IAAI,MAAM;YAClB;YAEA,qCAAqC;YACrC,MAAM,oBAAoB,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,EAAE,MAAM,KAAK;YACzF,IAAI,mBAAmB;gBACrB,MAAM,IAAI,MAAM;YAClB;YAEA,yBAAyB;YACzB,IAAI,eAAyB,EAAE;YAE/B,yBAAyB;YACzB,MAAM,oBAAoB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,EAAE,GAAG;YACnF,eAAe;mBAAI;mBAAiB,kBAAkB,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;aAAG;YAEvE,sCAAsC;YACtC,IAAI,cAAc,OAAO;gBACvB,MAAM,YAAY,SAAS,MAAM,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,OAAO;gBAC/D,eAAe;uBAAI;uBAAiB;iBAAU;YAChD;YAEA,MAAM,WAAW,MAAM,MAAM,gCAAgC;gBAC3D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,SAAS,KAAK,CAAC,IAAI;oBAC1B,OAAO,SAAS,KAAK,CAAC,IAAI;oBAC1B,OAAO;oBACP,WAAW;oBACX,eAAe,SAAS,aAAa,CAAC,IAAI;oBAC1C,aAAa,SAAS,WAAW,CAAC,IAAI;oBACtC,oBAAoB,SAAS,kBAAkB,CAAC,IAAI;oBACpD,qBAAqB,SAAS,mBAAmB,CAAC,IAAI,MAAM;oBAC5D,QAAQ,aAAa,GAAG,CAAC,CAAA,MAAO,CAAC;4BAAE,KAAK,IAAI,IAAI;wBAAG,CAAC;gBACtD;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW;gBACX,aAAa;gBACb,YAAY;oBACV,OAAO;oBACP,OAAO;oBACP,OAAO;oBACP,WAAW;oBACX,eAAe;oBACf,aAAa;oBACb,oBAAoB;oBACpB,qBAAqB;oBACrB,QAAQ,EAAE;gBACZ;gBACA,aAAa;oBAAC;iBAAG;gBACjB,iBAAiB,EAAE;gBACnB,aAAa;gBAEb,wCAAwC;gBACxC,WAAW;oBACT,OAAO,IAAI,CAAC;gBACd,GAAG;YACL,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;8CAEV,cAAA,6LAAC,4NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,KAAI;wDACJ,QAAQ;;;;;;;;;;;;0DAIZ,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,SAAS;wDACzB,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,KAAI;wDACJ,QAAQ;;;;;;;;;;;;;;;;;;kDAKd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,aAAa;gDAC7B,UAAU;gDACV,WAAU;gDACV,aAAY;gDACZ,QAAQ;;;;;;;;;;;;kDAIZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,MAAM;gDACN,WAAU;gDACV,aAAY;gDACZ,QAAQ;;;;;;;;;;;;;;;;;;0CAMd,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,kBAAkB;wDAClC,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,SAAQ;wDACR,QAAQ;;;;;;kEAEV,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAG5C,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,mBAAmB;wDACnC,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAOpB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;;4CACZ,UAAU,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,qBAAqB,OAAO,EAAE,MAAM,CAAC,KAAK;4DAC3D,WAAU;4DACV,aAAY;;;;;;wDAEb,UAAU,MAAM,GAAG,mBAClB,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,oBAAoB;4DACnC,WAAU;sEAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;mDAfjB;;;;;0DAoBZ,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;4BAOJ,uBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;4BAI1C,yBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAA0B;;;;;;;;;;;0CAK7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3C;GA/bwB;;QAIP,qIAAA,CAAA,YAAS;;;KAJF", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/shanmukha%20generators/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/shanmukha%20generators/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/shanmukha%20generators/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/shanmukha%20generators/node_modules/%40heroicons/react/24/outline/esm/ArrowLeftIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ArrowLeftIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowLeftIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,cAAc,EACrB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/shanmukha%20generators/node_modules/%40heroicons/react/24/outline/esm/PhotoIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PhotoIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhotoIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/shanmukha%20generators/node_modules/%40heroicons/react/24/outline/esm/XMarkIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction XMarkIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M6 18 18 6M6 6l12 12\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(XMarkIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}