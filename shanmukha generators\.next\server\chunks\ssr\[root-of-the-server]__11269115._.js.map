{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/shanmukha%20generators/src/utils/fileUpload.ts"], "sourcesContent": ["// File upload utilities and validation\n\nexport interface UploadedFile {\n  file: File;\n  id: string;\n  preview: string;\n  progress: number;\n  status: 'pending' | 'uploading' | 'success' | 'error';\n  error?: string;\n  url?: string;\n}\n\nexport interface FileValidationResult {\n  isValid: boolean;\n  error?: string;\n}\n\n// Supported file types\nexport const SUPPORTED_FILE_TYPES = {\n  'image/jpeg': ['.jpg', '.jpeg'],\n  'image/png': ['.png'],\n  'image/gif': ['.gif'],\n  'image/webp': ['.webp']\n};\n\n// File size limits\nexport const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB per file\nexport const MAX_TOTAL_SIZE = 25 * 1024 * 1024; // 25MB total\nexport const MAX_FILES = 10;\n\n/**\n * Validate a single file\n */\nexport function validateFile(file: File): FileValidationResult {\n  // Check file type\n  if (!Object.keys(SUPPORTED_FILE_TYPES).includes(file.type)) {\n    return {\n      isValid: false,\n      error: `Unsupported file type. Please use: ${Object.values(SUPPORTED_FILE_TYPES).flat().join(', ')}`\n    };\n  }\n\n  // Check file size\n  if (file.size > MAX_FILE_SIZE) {\n    return {\n      isValid: false,\n      error: `File size too large. Maximum size is ${formatFileSize(MAX_FILE_SIZE)}`\n    };\n  }\n\n  // Check file extension matches MIME type\n  const allowedExtensions = SUPPORTED_FILE_TYPES[file.type as keyof typeof SUPPORTED_FILE_TYPES];\n  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\n  \n  if (!allowedExtensions.includes(fileExtension)) {\n    return {\n      isValid: false,\n      error: 'File extension does not match file type'\n    };\n  }\n\n  return { isValid: true };\n}\n\n/**\n * Validate multiple files\n */\nexport function validateFiles(files: File[]): FileValidationResult {\n  // Check number of files\n  if (files.length > MAX_FILES) {\n    return {\n      isValid: false,\n      error: `Too many files. Maximum is ${MAX_FILES} files`\n    };\n  }\n\n  // Check total size\n  const totalSize = files.reduce((sum, file) => sum + file.size, 0);\n  if (totalSize > MAX_TOTAL_SIZE) {\n    return {\n      isValid: false,\n      error: `Total file size too large. Maximum is ${formatFileSize(MAX_TOTAL_SIZE)}`\n    };\n  }\n\n  // Validate each file\n  for (const file of files) {\n    const validation = validateFile(file);\n    if (!validation.isValid) {\n      return validation;\n    }\n  }\n\n  return { isValid: true };\n}\n\n/**\n * Create a preview URL for an image file\n */\nexport function createFilePreview(file: File): string {\n  return URL.createObjectURL(file);\n}\n\n/**\n * Generate a unique file ID\n */\nexport function generateFileId(): string {\n  return `file_${Date.now()}_${Math.random().toString(36).substring(7)}`;\n}\n\n/**\n * Format file size for display\n */\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\n/**\n * Upload a single file to the server\n */\nexport async function uploadFile(file: File, onProgress?: (progress: number) => void): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    const xhr = new XMLHttpRequest();\n\n    // Track upload progress\n    xhr.upload.addEventListener('progress', (event) => {\n      if (event.lengthComputable && onProgress) {\n        const progress = Math.round((event.loaded / event.total) * 100);\n        onProgress(progress);\n      }\n    });\n\n    xhr.addEventListener('load', () => {\n      if (xhr.status === 200) {\n        try {\n          const response = JSON.parse(xhr.responseText);\n          if (response.success) {\n            resolve(response.data.url);\n          } else {\n            reject(new Error(response.error || 'Upload failed'));\n          }\n        } catch (error) {\n          reject(new Error('Invalid response from server'));\n        }\n      } else {\n        reject(new Error(`Upload failed with status ${xhr.status}`));\n      }\n    });\n\n    xhr.addEventListener('error', () => {\n      reject(new Error('Network error during upload'));\n    });\n\n    xhr.addEventListener('abort', () => {\n      reject(new Error('Upload was cancelled'));\n    });\n\n    xhr.open('POST', '/api/admin/upload');\n    xhr.send(formData);\n  });\n}\n\n/**\n * Upload multiple files with progress tracking\n */\nexport async function uploadFiles(\n  files: UploadedFile[],\n  onProgress: (fileId: string, progress: number) => void,\n  onComplete: (fileId: string, url: string) => void,\n  onError: (fileId: string, error: string) => void\n): Promise<void> {\n  const uploadPromises = files.map(async (uploadedFile) => {\n    try {\n      const url = await uploadFile(uploadedFile.file, (progress) => {\n        onProgress(uploadedFile.id, progress);\n      });\n      onComplete(uploadedFile.id, url);\n    } catch (error) {\n      onError(uploadedFile.id, error instanceof Error ? error.message : 'Upload failed');\n    }\n  });\n\n  await Promise.all(uploadPromises);\n}\n\n/**\n * Clean up preview URLs to prevent memory leaks\n */\nexport function cleanupPreviews(files: UploadedFile[]): void {\n  files.forEach(file => {\n    if (file.preview) {\n      URL.revokeObjectURL(file.preview);\n    }\n  });\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;;;;;;;;;;;;AAkBhC,MAAM,uBAAuB;IAClC,cAAc;QAAC;QAAQ;KAAQ;IAC/B,aAAa;QAAC;KAAO;IACrB,aAAa;QAAC;KAAO;IACrB,cAAc;QAAC;KAAQ;AACzB;AAGO,MAAM,gBAAgB,IAAI,OAAO,MAAM,eAAe;AACtD,MAAM,iBAAiB,KAAK,OAAO,MAAM,aAAa;AACtD,MAAM,YAAY;AAKlB,SAAS,aAAa,IAAU;IACrC,kBAAkB;IAClB,IAAI,CAAC,OAAO,IAAI,CAAC,sBAAsB,QAAQ,CAAC,KAAK,IAAI,GAAG;QAC1D,OAAO;YACL,SAAS;YACT,OAAO,CAAC,mCAAmC,EAAE,OAAO,MAAM,CAAC,sBAAsB,IAAI,GAAG,IAAI,CAAC,OAAO;QACtG;IACF;IAEA,kBAAkB;IAClB,IAAI,KAAK,IAAI,GAAG,eAAe;QAC7B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,qCAAqC,EAAE,eAAe,gBAAgB;QAChF;IACF;IAEA,yCAAyC;IACzC,MAAM,oBAAoB,oBAAoB,CAAC,KAAK,IAAI,CAAsC;IAC9F,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;IAExD,IAAI,CAAC,kBAAkB,QAAQ,CAAC,gBAAgB;QAC9C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,SAAS,cAAc,KAAa;IACzC,wBAAwB;IACxB,IAAI,MAAM,MAAM,GAAG,WAAW;QAC5B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,2BAA2B,EAAE,UAAU,MAAM,CAAC;QACxD;IACF;IAEA,mBAAmB;IACnB,MAAM,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,IAAI,EAAE;IAC/D,IAAI,YAAY,gBAAgB;QAC9B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,sCAAsC,EAAE,eAAe,iBAAiB;QAClF;IACF;IAEA,qBAAqB;IACrB,KAAK,MAAM,QAAQ,MAAO;QACxB,MAAM,aAAa,aAAa;QAChC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO;QACT;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,SAAS,kBAAkB,IAAU;IAC1C,OAAO,IAAI,eAAe,CAAC;AAC7B;AAKO,SAAS;IACd,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,IAAI;AACxE;AAKO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAKO,eAAe,WAAW,IAAU,EAAE,UAAuC;IAClF,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,MAAM,MAAM,IAAI;QAEhB,wBAAwB;QACxB,IAAI,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC;YACvC,IAAI,MAAM,gBAAgB,IAAI,YAAY;gBACxC,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;gBAC3D,WAAW;YACb;QACF;QAEA,IAAI,gBAAgB,CAAC,QAAQ;YAC3B,IAAI,IAAI,MAAM,KAAK,KAAK;gBACtB,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;oBAC5C,IAAI,SAAS,OAAO,EAAE;wBACpB,QAAQ,SAAS,IAAI,CAAC,GAAG;oBAC3B,OAAO;wBACL,OAAO,IAAI,MAAM,SAAS,KAAK,IAAI;oBACrC;gBACF,EAAE,OAAO,OAAO;oBACd,OAAO,IAAI,MAAM;gBACnB;YACF,OAAO;gBACL,OAAO,IAAI,MAAM,CAAC,0BAA0B,EAAE,IAAI,MAAM,EAAE;YAC5D;QACF;QAEA,IAAI,gBAAgB,CAAC,SAAS;YAC5B,OAAO,IAAI,MAAM;QACnB;QAEA,IAAI,gBAAgB,CAAC,SAAS;YAC5B,OAAO,IAAI,MAAM;QACnB;QAEA,IAAI,IAAI,CAAC,QAAQ;QACjB,IAAI,IAAI,CAAC;IACX;AACF;AAKO,eAAe,YACpB,KAAqB,EACrB,UAAsD,EACtD,UAAiD,EACjD,OAAgD;IAEhD,MAAM,iBAAiB,MAAM,GAAG,CAAC,OAAO;QACtC,IAAI;YACF,MAAM,MAAM,MAAM,WAAW,aAAa,IAAI,EAAE,CAAC;gBAC/C,WAAW,aAAa,EAAE,EAAE;YAC9B;YACA,WAAW,aAAa,EAAE,EAAE;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,aAAa,EAAE,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpE;IACF;IAEA,MAAM,QAAQ,GAAG,CAAC;AACpB;AAKO,SAAS,gBAAgB,KAAqB;IACnD,MAAM,OAAO,CAAC,CAAA;QACZ,IAAI,KAAK,OAAO,EAAE;YAChB,IAAI,eAAe,CAAC,KAAK,OAAO;QAClC;IACF;AACF", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/shanmukha%20generators/src/components/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useCallback, useState, useEffect } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport {\n  PhotoIcon,\n  XMarkIcon,\n  ArrowUpTrayIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n} from '@heroicons/react/24/outline';\nimport {\n  UploadedFile,\n  validateFiles,\n  createFilePreview,\n  generateFileId,\n  formatFileSize,\n  cleanupPreviews,\n  SUPPORTED_FILE_TYPES,\n  MAX_FILE_SIZE,\n  MAX_FILES,\n} from '@/utils/fileUpload';\n\ninterface FileUploadProps {\n  onFilesChange: (files: UploadedFile[]) => void;\n  maxFiles?: number;\n  disabled?: boolean;\n  existingFiles?: UploadedFile[];\n}\n\nexport default function FileUpload({\n  onFilesChange,\n  maxFiles = MAX_FILES,\n  disabled = false,\n  existingFiles = [],\n}: FileUploadProps) {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>(existingFiles);\n  const [error, setError] = useState<string>('');\n\n  // Clean up preview URLs on unmount\n  useEffect(() => {\n    return () => {\n      cleanupPreviews(uploadedFiles);\n    };\n  }, []);\n\n  // Update parent when files change\n  useEffect(() => {\n    onFilesChange(uploadedFiles);\n  }, [uploadedFiles, onFilesChange]);\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    setError('');\n\n    // Check if adding these files would exceed the limit\n    if (uploadedFiles.length + acceptedFiles.length > maxFiles) {\n      setError(`Cannot add more than ${maxFiles} files`);\n      return;\n    }\n\n    // Validate files\n    const validation = validateFiles(acceptedFiles);\n    if (!validation.isValid) {\n      setError(validation.error || 'Invalid files');\n      return;\n    }\n\n    // Create uploaded file objects\n    const newFiles: UploadedFile[] = acceptedFiles.map((file) => ({\n      file,\n      id: generateFileId(),\n      preview: createFilePreview(file),\n      progress: 0,\n      status: 'pending',\n    }));\n\n    setUploadedFiles((prev) => [...prev, ...newFiles]);\n  }, [uploadedFiles.length, maxFiles]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: SUPPORTED_FILE_TYPES,\n    maxSize: MAX_FILE_SIZE,\n    disabled,\n    multiple: true,\n  });\n\n  const removeFile = (fileId: string) => {\n    setUploadedFiles((prev) => {\n      const fileToRemove = prev.find(f => f.id === fileId);\n      if (fileToRemove?.preview) {\n        URL.revokeObjectURL(fileToRemove.preview);\n      }\n      return prev.filter(f => f.id !== fileId);\n    });\n  };\n\n  const updateFileProgress = (fileId: string, progress: number) => {\n    setUploadedFiles((prev) =>\n      prev.map((file) =>\n        file.id === fileId ? { ...file, progress, status: 'uploading' } : file\n      )\n    );\n  };\n\n  const updateFileStatus = (fileId: string, status: UploadedFile['status'], url?: string, error?: string) => {\n    setUploadedFiles((prev) =>\n      prev.map((file) =>\n        file.id === fileId ? { ...file, status, url, error } : file\n      )\n    );\n  };\n\n  const getStatusIcon = (file: UploadedFile) => {\n    switch (file.status) {\n      case 'success':\n        return <CheckCircleIcon className=\"h-5 w-5 text-green-500\" />;\n      case 'error':\n        return <ExclamationTriangleIcon className=\"h-5 w-5 text-red-500\" />;\n      case 'uploading':\n        return (\n          <div className=\"h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin\" />\n        );\n      default:\n        return <PhotoIcon className=\"h-5 w-5 text-gray-400\" />;\n    }\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Drop Zone */}\n      <div\n        {...getRootProps()}\n        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${\n          isDragActive\n            ? 'border-blue-500 bg-blue-50'\n            : disabled\n            ? 'border-gray-200 bg-gray-50 cursor-not-allowed'\n            : 'border-gray-300 hover:border-gray-400'\n        }`}\n      >\n        <input {...getInputProps()} />\n        <ArrowUpTrayIcon className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n        \n        {isDragActive ? (\n          <p className=\"text-blue-600 font-medium\">Drop the files here...</p>\n        ) : (\n          <div>\n            <p className=\"text-gray-600 font-medium mb-2\">\n              {disabled ? 'File upload disabled' : 'Drag & drop images here, or click to select'}\n            </p>\n            <p className=\"text-sm text-gray-500\">\n              Supports: JPEG, PNG, GIF, WebP • Max {formatFileSize(MAX_FILE_SIZE)} per file • Up to {maxFiles} files\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"rounded-md bg-red-50 p-4\">\n          <div className=\"flex\">\n            <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-red-700\">{error}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* File List */}\n      {uploadedFiles.length > 0 && (\n        <div className=\"space-y-3\">\n          <h4 className=\"text-sm font-medium text-gray-900\">\n            Selected Files ({uploadedFiles.length}/{maxFiles})\n          </h4>\n          \n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {uploadedFiles.map((file) => (\n              <div\n                key={file.id}\n                className=\"relative bg-white border border-gray-200 rounded-lg p-3 shadow-sm\"\n              >\n                {/* Remove Button */}\n                <button\n                  type=\"button\"\n                  onClick={() => removeFile(file.id)}\n                  className=\"absolute top-2 right-2 p-1 bg-red-100 hover:bg-red-200 rounded-full transition-colors\"\n                  disabled={file.status === 'uploading'}\n                >\n                  <XMarkIcon className=\"h-4 w-4 text-red-600\" />\n                </button>\n\n                {/* Image Preview */}\n                <div className=\"aspect-square mb-3 bg-gray-100 rounded-lg overflow-hidden\">\n                  <img\n                    src={file.preview}\n                    alt={file.file.name}\n                    className=\"w-full h-full object-cover\"\n                  />\n                </div>\n\n                {/* File Info */}\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center space-x-2\">\n                    {getStatusIcon(file)}\n                    <span className=\"text-sm font-medium text-gray-900 truncate\">\n                      {file.file.name}\n                    </span>\n                  </div>\n                  \n                  <div className=\"text-xs text-gray-500\">\n                    {formatFileSize(file.file.size)}\n                  </div>\n\n                  {/* Progress Bar */}\n                  {file.status === 'uploading' && (\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div\n                        className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                        style={{ width: `${file.progress}%` }}\n                      />\n                    </div>\n                  )}\n\n                  {/* Error Message */}\n                  {file.status === 'error' && file.error && (\n                    <p className=\"text-xs text-red-600\">{file.error}</p>\n                  )}\n\n                  {/* Success Message */}\n                  {file.status === 'success' && (\n                    <p className=\"text-xs text-green-600\">Upload complete</p>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Upload Summary */}\n      {uploadedFiles.length > 0 && (\n        <div className=\"text-sm text-gray-600\">\n          Total size: {formatFileSize(uploadedFiles.reduce((sum, file) => sum + file.file.size, 0))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAXA;;;;;;AA8Be,SAAS,WAAW,EACjC,aAAa,EACb,WAAW,0HAAA,CAAA,YAAS,EACpB,WAAW,KAAK,EAChB,gBAAgB,EAAE,EACF;IAChB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE;QAClB;IACF,GAAG,EAAE;IAEL,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG;QAAC;QAAe;KAAc;IAEjC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1B,SAAS;QAET,qDAAqD;QACrD,IAAI,cAAc,MAAM,GAAG,cAAc,MAAM,GAAG,UAAU;YAC1D,SAAS,CAAC,qBAAqB,EAAE,SAAS,MAAM,CAAC;YACjD;QACF;QAEA,iBAAiB;QACjB,MAAM,aAAa,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,EAAE;QACjC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,SAAS,WAAW,KAAK,IAAI;YAC7B;QACF;QAEA,+BAA+B;QAC/B,MAAM,WAA2B,cAAc,GAAG,CAAC,CAAC,OAAS,CAAC;gBAC5D;gBACA,IAAI,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD;gBACjB,SAAS,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAE;gBAC3B,UAAU;gBACV,QAAQ;YACV,CAAC;QAED,iBAAiB,CAAC,OAAS;mBAAI;mBAAS;aAAS;IACnD,GAAG;QAAC,cAAc,MAAM;QAAE;KAAS;IAEnC,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ,0HAAA,CAAA,uBAAoB;QAC5B,SAAS,0HAAA,CAAA,gBAAa;QACtB;QACA,UAAU;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAC;YAChB,MAAM,eAAe,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC7C,IAAI,cAAc,SAAS;gBACzB,IAAI,eAAe,CAAC,aAAa,OAAO;YAC1C;YACA,OAAO,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACnC;IACF;IAEA,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,iBAAiB,CAAC,OAChB,KAAK,GAAG,CAAC,CAAC,OACR,KAAK,EAAE,KAAK,SAAS;oBAAE,GAAG,IAAI;oBAAE;oBAAU,QAAQ;gBAAY,IAAI;IAGxE;IAEA,MAAM,mBAAmB,CAAC,QAAgB,QAAgC,KAAc;QACtF,iBAAiB,CAAC,OAChB,KAAK,GAAG,CAAC,CAAC,OACR,KAAK,EAAE,KAAK,SAAS;oBAAE,GAAG,IAAI;oBAAE;oBAAQ;oBAAK;gBAAM,IAAI;IAG7D;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,qBAAO,8OAAC,6NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,6OAAA,CAAA,0BAAuB;oBAAC,WAAU;;;;;;YAC5C,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;;;;;YAEnB;gBACE,qBAAO,8OAAC,iNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC,mFAAmF,EAC7F,eACI,+BACA,WACA,kDACA,yCACJ;;kCAEF,8OAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,8OAAC,6NAAA,CAAA,kBAAe;wBAAC,WAAU;;;;;;oBAE1B,6BACC,8OAAC;wBAAE,WAAU;kCAA4B;;;;;6CAEzC,8OAAC;;0CACC,8OAAC;gCAAE,WAAU;0CACV,WAAW,yBAAyB;;;;;;0CAEvC,8OAAC;gCAAE,WAAU;;oCAAwB;oCACG,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,gBAAa;oCAAE;oCAAmB;oCAAS;;;;;;;;;;;;;;;;;;;YAOvG,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6OAAA,CAAA,0BAAuB;4BAAC,WAAU;;;;;;sCACnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;YAO5C,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAoC;4BAC/B,cAAc,MAAM;4BAAC;4BAAE;4BAAS;;;;;;;kCAGnD,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;gCAEC,WAAU;;kDAGV,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,WAAW,KAAK,EAAE;wCACjC,WAAU;wCACV,UAAU,KAAK,MAAM,KAAK;kDAE1B,cAAA,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAIvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAK,KAAK,OAAO;4CACjB,KAAK,KAAK,IAAI,CAAC,IAAI;4CACnB,WAAU;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDACZ,cAAc;kEACf,8OAAC;wDAAK,WAAU;kEACb,KAAK,IAAI,CAAC,IAAI;;;;;;;;;;;;0DAInB,8OAAC;gDAAI,WAAU;0DACZ,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,IAAI,CAAC,IAAI;;;;;;4CAI/B,KAAK,MAAM,KAAK,6BACf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC;oDAAC;;;;;;;;;;;4CAMzC,KAAK,MAAM,KAAK,WAAW,KAAK,KAAK,kBACpC,8OAAC;gDAAE,WAAU;0DAAwB,KAAK,KAAK;;;;;;4CAIhD,KAAK,MAAM,KAAK,2BACf,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;;+BApDrC,KAAK,EAAE;;;;;;;;;;;;;;;;YA8DrB,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAI,WAAU;;oBAAwB;oBACxB,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE;;;;;;;;;;;;;AAKhG", "debugId": null}}, {"offset": {"line": 606, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/shanmukha%20generators/src/app/admin/add-generator/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { ArrowLeftIcon, PhotoIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport FileUpload from '@/components/FileUpload';\nimport { UploadedFile, uploadFiles } from '@/utils/fileUpload';\n\nexport default function AddGenerator() {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const router = useRouter();\n\n  const [formData, setFormData] = useState({\n    brand: '',\n    model: '',\n    price: '',\n    hours_run: '',\n    location_text: '',\n    description: '',\n    seller_whatsapp_id: '',\n    seller_display_name: '',\n    images: [] as string[]\n  });\n\n  const [imageUrls, setImageUrls] = useState<string[]>(['']);\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [imageMode, setImageMode] = useState<'upload' | 'url'>('upload');\n  const [isUploading, setIsUploading] = useState(false);\n\n  useEffect(() => {\n    checkAuth();\n  }, []);\n\n  const checkAuth = async () => {\n    try {\n      const response = await fetch('/api/admin/auth');\n      const data = await response.json();\n      \n      if (!data.success) {\n        router.push('/admin/login');\n      }\n    } catch (error) {\n      router.push('/admin/login');\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleImageUrlChange = (index: number, value: string) => {\n    const newImageUrls = [...imageUrls];\n    newImageUrls[index] = value;\n    setImageUrls(newImageUrls);\n    \n    // Update form data with non-empty URLs\n    setFormData(prev => ({\n      ...prev,\n      images: newImageUrls.filter(url => url.trim() !== '')\n    }));\n  };\n\n  const addImageUrlField = () => {\n    setImageUrls([...imageUrls, '']);\n  };\n\n  const removeImageUrlField = (index: number) => {\n    const newImageUrls = imageUrls.filter((_, i) => i !== index);\n    setImageUrls(newImageUrls);\n    setFormData(prev => ({\n      ...prev,\n      images: newImageUrls.filter(url => url.trim() !== '')\n    }));\n  };\n\n  const handleFilesChange = (files: UploadedFile[]) => {\n    setUploadedFiles(files);\n  };\n\n  const handleFileUpload = async () => {\n    if (uploadedFiles.length === 0) return [];\n\n    setIsUploading(true);\n    const uploadedUrls: string[] = [];\n\n    try {\n      await uploadFiles(\n        uploadedFiles.filter(f => f.status === 'pending'),\n        (fileId, progress) => {\n          setUploadedFiles(prev =>\n            prev.map(file =>\n              file.id === fileId ? { ...file, progress, status: 'uploading' } : file\n            )\n          );\n        },\n        (fileId, url) => {\n          setUploadedFiles(prev =>\n            prev.map(file =>\n              file.id === fileId ? { ...file, url, status: 'success' } : file\n            )\n          );\n          uploadedUrls.push(url);\n        },\n        (fileId, error) => {\n          setUploadedFiles(prev =>\n            prev.map(file =>\n              file.id === fileId ? { ...file, error, status: 'error' } : file\n            )\n          );\n        }\n      );\n\n      return uploadedUrls;\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      // Validate required fields\n      if (!formData.brand || !formData.model || !formData.price || !formData.hours_run ||\n          !formData.location_text || !formData.description || !formData.seller_whatsapp_id) {\n        throw new Error('Please fill in all required fields');\n      }\n\n      // Validate price and hours are numbers\n      const price = parseInt(formData.price);\n      const hours = parseInt(formData.hours_run);\n\n      if (isNaN(price) || price <= 0) {\n        throw new Error('Please enter a valid price');\n      }\n\n      if (isNaN(hours) || hours < 0) {\n        throw new Error('Please enter valid running hours');\n      }\n\n      // Validate WhatsApp ID format\n      if (!/^\\d{10,15}$/.test(formData.seller_whatsapp_id)) {\n        throw new Error('Please enter a valid WhatsApp ID (10-15 digits)');\n      }\n\n      // Check if there are pending uploads\n      const hasPendingUploads = uploadedFiles.some(f => f.status === 'pending' || f.status === 'uploading');\n      if (hasPendingUploads) {\n        throw new Error('Please wait for all file uploads to complete');\n      }\n\n      // Collect all image URLs\n      let allImageUrls: string[] = [];\n\n      // Add uploaded file URLs\n      const successfulUploads = uploadedFiles.filter(f => f.status === 'success' && f.url);\n      allImageUrls = [...allImageUrls, ...successfulUploads.map(f => f.url!)];\n\n      // Add manual URLs (if using URL mode)\n      if (imageMode === 'url') {\n        const validUrls = formData.images.filter(url => url.trim() !== '');\n        allImageUrls = [...allImageUrls, ...validUrls];\n      }\n\n      const response = await fetch('/api/admin/generators/manual', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          brand: formData.brand.trim(),\n          model: formData.model.trim(),\n          price: price,\n          hours_run: hours,\n          location_text: formData.location_text.trim(),\n          description: formData.description.trim(),\n          seller_whatsapp_id: formData.seller_whatsapp_id.trim(),\n          seller_display_name: formData.seller_display_name.trim() || undefined,\n          images: allImageUrls.map(url => ({ url: url.trim() }))\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setSuccess('Generator added successfully!');\n        // Reset form\n        setFormData({\n          brand: '',\n          model: '',\n          price: '',\n          hours_run: '',\n          location_text: '',\n          description: '',\n          seller_whatsapp_id: '',\n          seller_display_name: '',\n          images: []\n        });\n        setImageUrls(['']);\n        setUploadedFiles([]);\n        setImageMode('upload');\n        \n        // Redirect to dashboard after 2 seconds\n        setTimeout(() => {\n          router.push('/admin/dashboard');\n        }, 2000);\n      } else {\n        setError(data.error || 'Failed to add generator');\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'An error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => router.push('/admin/dashboard')}\n                className=\"text-gray-600 hover:text-gray-900\"\n              >\n                <ArrowLeftIcon className=\"h-6 w-6\" />\n              </button>\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900\">Add Generator Manually</h1>\n                <p className=\"text-gray-600\">Create a new generator listing</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Generator Information */}\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Generator Information</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Brand *\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"brand\"\n                    value={formData.brand}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"e.g., Kirloskar, Mahindra, Cummins\"\n                    required\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Model *\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"model\"\n                    value={formData.model}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"e.g., KG1-62.5AS, MDG-125\"\n                    required\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Price (₹) *\n                  </label>\n                  <input\n                    type=\"number\"\n                    name=\"price\"\n                    value={formData.price}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"850000\"\n                    min=\"0\"\n                    required\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Running Hours *\n                  </label>\n                  <input\n                    type=\"number\"\n                    name=\"hours_run\"\n                    value={formData.hours_run}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"12500\"\n                    min=\"0\"\n                    required\n                  />\n                </div>\n              </div>\n              \n              <div className=\"mt-4\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Location *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"location_text\"\n                  value={formData.location_text}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"Mumbai, Maharashtra\"\n                  required\n                />\n              </div>\n              \n              <div className=\"mt-4\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Description *\n                </label>\n                <textarea\n                  name=\"description\"\n                  value={formData.description}\n                  onChange={handleInputChange}\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"Excellent condition diesel generator, well maintained with all documents. Recently serviced and ready for immediate use.\"\n                  required\n                />\n              </div>\n            </div>\n\n            {/* Seller Information */}\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Seller Information</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    WhatsApp ID *\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"seller_whatsapp_id\"\n                    value={formData.seller_whatsapp_id}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"919876543210\"\n                    pattern=\"\\d{10,15}\"\n                    required\n                  />\n                  <p className=\"text-xs text-gray-500 mt-1\">10-15 digits, no spaces or symbols</p>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Display Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"seller_display_name\"\n                    value={formData.seller_display_name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"John Seller (optional)\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Images */}\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Images</h3>\n\n              {/* Image Mode Tabs */}\n              <div className=\"mb-4\">\n                <div className=\"border-b border-gray-200\">\n                  <nav className=\"-mb-px flex space-x-8\">\n                    <button\n                      type=\"button\"\n                      onClick={() => setImageMode('upload')}\n                      className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                        imageMode === 'upload'\n                          ? 'border-blue-500 text-blue-600'\n                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                      }`}\n                    >\n                      Upload Files\n                    </button>\n                    <button\n                      type=\"button\"\n                      onClick={() => setImageMode('url')}\n                      className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                        imageMode === 'url'\n                          ? 'border-blue-500 text-blue-600'\n                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                      }`}\n                    >\n                      Enter URLs\n                    </button>\n                  </nav>\n                </div>\n              </div>\n\n              {/* Upload Mode */}\n              {imageMode === 'upload' && (\n                <div className=\"space-y-4\">\n                  <FileUpload\n                    onFilesChange={handleFilesChange}\n                    maxFiles={10}\n                    disabled={isLoading}\n                    existingFiles={uploadedFiles}\n                  />\n\n                  {uploadedFiles.length > 0 && (\n                    <div className=\"text-sm text-gray-600\">\n                      <p>\n                        {uploadedFiles.filter(f => f.status === 'success').length} of {uploadedFiles.length} files uploaded successfully\n                      </p>\n                      {uploadedFiles.some(f => f.status === 'error') && (\n                        <p className=\"text-red-600 mt-1\">\n                          Some files failed to upload. Please remove them or try again.\n                        </p>\n                      )}\n                    </div>\n                  )}\n                </div>\n              )}\n\n              {/* URL Mode */}\n              {imageMode === 'url' && (\n                <div className=\"space-y-3\">\n                  {imageUrls.map((url, index) => (\n                    <div key={index} className=\"flex items-center space-x-2\">\n                      <PhotoIcon className=\"h-5 w-5 text-gray-400\" />\n                      <input\n                        type=\"url\"\n                        value={url}\n                        onChange={(e) => handleImageUrlChange(index, e.target.value)}\n                        className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        placeholder=\"https://example.com/image.jpg\"\n                      />\n                      {imageUrls.length > 1 && (\n                        <button\n                          type=\"button\"\n                          onClick={() => removeImageUrlField(index)}\n                          className=\"text-red-500 hover:text-red-700\"\n                        >\n                          <XMarkIcon className=\"h-5 w-5\" />\n                        </button>\n                      )}\n                    </div>\n                  ))}\n                  <button\n                    type=\"button\"\n                    onClick={addImageUrlField}\n                    className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\"\n                  >\n                    + Add another image URL\n                  </button>\n                </div>\n              )}\n            </div>\n\n            {/* Error/Success Messages */}\n            {error && (\n              <div className=\"rounded-md bg-red-50 p-4\">\n                <div className=\"text-sm text-red-700\">{error}</div>\n              </div>\n            )}\n\n            {success && (\n              <div className=\"rounded-md bg-green-50 p-4\">\n                <div className=\"text-sm text-green-700\">{success}</div>\n              </div>\n            )}\n\n            {/* Submit Button */}\n            <div className=\"flex justify-end space-x-4\">\n              <button\n                type=\"button\"\n                onClick={() => router.push('/admin/dashboard')}\n                className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                disabled={isLoading || isUploading || uploadedFiles.some(f => f.status === 'uploading')}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isLoading ? 'Adding Generator...' :\n                 isUploading ? 'Uploading Files...' :\n                 uploadedFiles.some(f => f.status === 'uploading') ? 'Files Uploading...' :\n                 'Add Generator'}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,OAAO;QACP,OAAO;QACP,WAAW;QACX,eAAe;QACf,aAAa;QACb,oBAAoB;QACpB,qBAAqB;QACrB,QAAQ,EAAE;IACZ;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAG;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,uBAAuB,CAAC,OAAe;QAC3C,MAAM,eAAe;eAAI;SAAU;QACnC,YAAY,CAAC,MAAM,GAAG;QACtB,aAAa;QAEb,uCAAuC;QACvC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,OAAO;YACpD,CAAC;IACH;IAEA,MAAM,mBAAmB;QACvB,aAAa;eAAI;YAAW;SAAG;IACjC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,eAAe,UAAU,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACtD,aAAa;QACb,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,OAAO;YACpD,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;IACnB;IAEA,MAAM,mBAAmB;QACvB,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,EAAE;QAEzC,eAAe;QACf,MAAM,eAAyB,EAAE;QAEjC,IAAI;YACF,MAAM,CAAA,GAAA,0HAAA,CAAA,cAAW,AAAD,EACd,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YACvC,CAAC,QAAQ;gBACP,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,SAAS;4BAAE,GAAG,IAAI;4BAAE;4BAAU,QAAQ;wBAAY,IAAI;YAGxE,GACA,CAAC,QAAQ;gBACP,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,SAAS;4BAAE,GAAG,IAAI;4BAAE;4BAAK,QAAQ;wBAAU,IAAI;gBAG/D,aAAa,IAAI,CAAC;YACpB,GACA,CAAC,QAAQ;gBACP,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,SAAS;4BAAE,GAAG,IAAI;4BAAE;4BAAO,QAAQ;wBAAQ,IAAI;YAGjE;YAGF,OAAO;QACT,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QACT,WAAW;QAEX,IAAI;YACF,2BAA2B;YAC3B,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,SAAS,IAC5E,CAAC,SAAS,aAAa,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,kBAAkB,EAAE;gBACpF,MAAM,IAAI,MAAM;YAClB;YAEA,uCAAuC;YACvC,MAAM,QAAQ,SAAS,SAAS,KAAK;YACrC,MAAM,QAAQ,SAAS,SAAS,SAAS;YAEzC,IAAI,MAAM,UAAU,SAAS,GAAG;gBAC9B,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,MAAM,UAAU,QAAQ,GAAG;gBAC7B,MAAM,IAAI,MAAM;YAClB;YAEA,8BAA8B;YAC9B,IAAI,CAAC,cAAc,IAAI,CAAC,SAAS,kBAAkB,GAAG;gBACpD,MAAM,IAAI,MAAM;YAClB;YAEA,qCAAqC;YACrC,MAAM,oBAAoB,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,EAAE,MAAM,KAAK;YACzF,IAAI,mBAAmB;gBACrB,MAAM,IAAI,MAAM;YAClB;YAEA,yBAAyB;YACzB,IAAI,eAAyB,EAAE;YAE/B,yBAAyB;YACzB,MAAM,oBAAoB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,EAAE,GAAG;YACnF,eAAe;mBAAI;mBAAiB,kBAAkB,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;aAAG;YAEvE,sCAAsC;YACtC,IAAI,cAAc,OAAO;gBACvB,MAAM,YAAY,SAAS,MAAM,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,OAAO;gBAC/D,eAAe;uBAAI;uBAAiB;iBAAU;YAChD;YAEA,MAAM,WAAW,MAAM,MAAM,gCAAgC;gBAC3D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,SAAS,KAAK,CAAC,IAAI;oBAC1B,OAAO,SAAS,KAAK,CAAC,IAAI;oBAC1B,OAAO;oBACP,WAAW;oBACX,eAAe,SAAS,aAAa,CAAC,IAAI;oBAC1C,aAAa,SAAS,WAAW,CAAC,IAAI;oBACtC,oBAAoB,SAAS,kBAAkB,CAAC,IAAI;oBACpD,qBAAqB,SAAS,mBAAmB,CAAC,IAAI,MAAM;oBAC5D,QAAQ,aAAa,GAAG,CAAC,CAAA,MAAO,CAAC;4BAAE,KAAK,IAAI,IAAI;wBAAG,CAAC;gBACtD;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW;gBACX,aAAa;gBACb,YAAY;oBACV,OAAO;oBACP,OAAO;oBACP,OAAO;oBACP,WAAW;oBACX,eAAe;oBACf,aAAa;oBACb,oBAAoB;oBACpB,qBAAqB;oBACrB,QAAQ,EAAE;gBACZ;gBACA,aAAa;oBAAC;iBAAG;gBACjB,iBAAiB,EAAE;gBACnB,aAAa;gBAEb,wCAAwC;gBACxC,WAAW;oBACT,OAAO,IAAI,CAAC;gBACd,GAAG;YACL,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;8CAEV,cAAA,8OAAC,yNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,KAAI;wDACJ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,SAAS;wDACzB,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,KAAI;wDACJ,QAAQ;;;;;;;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,aAAa;gDAC7B,UAAU;gDACV,WAAU;gDACV,aAAY;gDACZ,QAAQ;;;;;;;;;;;;kDAIZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,MAAM;gDACN,WAAU;gDACV,aAAY;gDACZ,QAAQ;;;;;;;;;;;;;;;;;;0CAMd,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,kBAAkB;wDAClC,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,SAAQ;wDACR,QAAQ;;;;;;kEAEV,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAG5C,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,mBAAmB;wDACnC,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAOpB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,aAAa;wDAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,WACV,kCACA,8EACJ;kEACH;;;;;;kEAGD,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,aAAa;wDAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,QACV,kCACA,8EACJ;kEACH;;;;;;;;;;;;;;;;;;;;;;oCAQN,cAAc,0BACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,UAAU;gDACT,eAAe;gDACf,UAAU;gDACV,UAAU;gDACV,eAAe;;;;;;4CAGhB,cAAc,MAAM,GAAG,mBACtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DACE,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;4DAAC;4DAAK,cAAc,MAAM;4DAAC;;;;;;;oDAErF,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,0BACpC,8OAAC;wDAAE,WAAU;kEAAoB;;;;;;;;;;;;;;;;;;oCAU1C,cAAc,uBACb,8OAAC;wCAAI,WAAU;;4CACZ,UAAU,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC,iNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,qBAAqB,OAAO,EAAE,MAAM,CAAC,KAAK;4DAC3D,WAAU;4DACV,aAAY;;;;;;wDAEb,UAAU,MAAM,GAAG,mBAClB,8OAAC;4DACC,MAAK;4DACL,SAAS,IAAM,oBAAoB;4DACnC,WAAU;sEAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;mDAfjB;;;;;0DAoBZ,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;4BAQN,uBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;4BAI1C,yBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAA0B;;;;;;;;;;;0CAK7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,UAAU,aAAa,eAAe,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;wCAC3E,WAAU;kDAET,YAAY,wBACZ,cAAc,uBACd,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,uBACpD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB", "debugId": null}}]}