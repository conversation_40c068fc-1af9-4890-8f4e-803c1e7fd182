'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon, PhotoIcon, XMarkIcon } from '@heroicons/react/24/outline';
import FileUpload from '@/components/FileUpload';
import { UploadedFile, uploadFiles } from '@/utils/fileUpload';

export default function AddGenerator() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const router = useRouter();

  const [formData, setFormData] = useState({
    brand: '',
    model: '',
    price: '',
    hours_run: '',
    location_text: '',
    description: '',
    seller_whatsapp_id: '',
    seller_display_name: '',
    images: [] as string[]
  });

  const [imageUrls, setImageUrls] = useState<string[]>(['']);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [imageMode, setImageMode] = useState<'upload' | 'url'>('upload');
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/admin/auth');
      const data = await response.json();
      
      if (!data.success) {
        router.push('/admin/login');
      }
    } catch (error) {
      router.push('/admin/login');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageUrlChange = (index: number, value: string) => {
    const newImageUrls = [...imageUrls];
    newImageUrls[index] = value;
    setImageUrls(newImageUrls);
    
    // Update form data with non-empty URLs
    setFormData(prev => ({
      ...prev,
      images: newImageUrls.filter(url => url.trim() !== '')
    }));
  };

  const addImageUrlField = () => {
    setImageUrls([...imageUrls, '']);
  };

  const removeImageUrlField = (index: number) => {
    const newImageUrls = imageUrls.filter((_, i) => i !== index);
    setImageUrls(newImageUrls);
    setFormData(prev => ({
      ...prev,
      images: newImageUrls.filter(url => url.trim() !== '')
    }));
  };

  const handleFilesChange = (files: UploadedFile[]) => {
    setUploadedFiles(files);
  };

  const handleFileUpload = async () => {
    if (uploadedFiles.length === 0) return [];

    setIsUploading(true);
    const uploadedUrls: string[] = [];

    try {
      await uploadFiles(
        uploadedFiles.filter(f => f.status === 'pending'),
        (fileId, progress) => {
          setUploadedFiles(prev =>
            prev.map(file =>
              file.id === fileId ? { ...file, progress, status: 'uploading' } : file
            )
          );
        },
        (fileId, url) => {
          setUploadedFiles(prev =>
            prev.map(file =>
              file.id === fileId ? { ...file, url, status: 'success' } : file
            )
          );
          uploadedUrls.push(url);
        },
        (fileId, error) => {
          setUploadedFiles(prev =>
            prev.map(file =>
              file.id === fileId ? { ...file, error, status: 'error' } : file
            )
          );
        }
      );

      return uploadedUrls;
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      // Validate required fields
      if (!formData.brand || !formData.model || !formData.price || !formData.hours_run ||
          !formData.location_text || !formData.description || !formData.seller_whatsapp_id) {
        throw new Error('Please fill in all required fields');
      }

      // Validate price and hours are numbers
      const price = parseInt(formData.price);
      const hours = parseInt(formData.hours_run);

      if (isNaN(price) || price <= 0) {
        throw new Error('Please enter a valid price');
      }

      if (isNaN(hours) || hours < 0) {
        throw new Error('Please enter valid running hours');
      }

      // Validate WhatsApp ID format
      if (!/^\d{10,15}$/.test(formData.seller_whatsapp_id)) {
        throw new Error('Please enter a valid WhatsApp ID (10-15 digits)');
      }

      // Check if there are pending uploads
      const hasPendingUploads = uploadedFiles.some(f => f.status === 'pending' || f.status === 'uploading');
      if (hasPendingUploads) {
        throw new Error('Please wait for all file uploads to complete');
      }

      // Collect all image URLs
      let allImageUrls: string[] = [];

      // Add uploaded file URLs
      const successfulUploads = uploadedFiles.filter(f => f.status === 'success' && f.url);
      allImageUrls = [...allImageUrls, ...successfulUploads.map(f => f.url!)];

      // Add manual URLs (if using URL mode)
      if (imageMode === 'url') {
        const validUrls = formData.images.filter(url => url.trim() !== '');
        allImageUrls = [...allImageUrls, ...validUrls];
      }

      const response = await fetch('/api/admin/generators/manual', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brand: formData.brand.trim(),
          model: formData.model.trim(),
          price: price,
          hours_run: hours,
          location_text: formData.location_text.trim(),
          description: formData.description.trim(),
          seller_whatsapp_id: formData.seller_whatsapp_id.trim(),
          seller_display_name: formData.seller_display_name.trim() || undefined,
          images: allImageUrls.map(url => ({ url: url.trim() }))
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Generator added successfully!');
        // Reset form
        setFormData({
          brand: '',
          model: '',
          price: '',
          hours_run: '',
          location_text: '',
          description: '',
          seller_whatsapp_id: '',
          seller_display_name: '',
          images: []
        });
        setImageUrls(['']);
        setUploadedFiles([]);
        setImageMode('upload');
        
        // Redirect to dashboard after 2 seconds
        setTimeout(() => {
          router.push('/admin/dashboard');
        }, 2000);
      } else {
        setError(data.error || 'Failed to add generator');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/admin/dashboard')}
                className="text-gray-600 hover:text-gray-900"
              >
                <ArrowLeftIcon className="h-6 w-6" />
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Add Generator Manually</h1>
                <p className="text-gray-600">Create a new generator listing</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Generator Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Generator Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Brand *
                  </label>
                  <input
                    type="text"
                    name="brand"
                    value={formData.brand}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., Kirloskar, Mahindra, Cummins"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Model *
                  </label>
                  <input
                    type="text"
                    name="model"
                    value={formData.model}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., KG1-62.5AS, MDG-125"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Price (₹) *
                  </label>
                  <input
                    type="number"
                    name="price"
                    value={formData.price}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="850000"
                    min="0"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Running Hours *
                  </label>
                  <input
                    type="number"
                    name="hours_run"
                    value={formData.hours_run}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="12500"
                    min="0"
                    required
                  />
                </div>
              </div>
              
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location *
                </label>
                <input
                  type="text"
                  name="location_text"
                  value={formData.location_text}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Mumbai, Maharashtra"
                  required
                />
              </div>
              
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description *
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Excellent condition diesel generator, well maintained with all documents. Recently serviced and ready for immediate use."
                  required
                />
              </div>
            </div>

            {/* Seller Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Seller Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    WhatsApp ID *
                  </label>
                  <input
                    type="text"
                    name="seller_whatsapp_id"
                    value={formData.seller_whatsapp_id}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="919876543210"
                    pattern="\d{10,15}"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">10-15 digits, no spaces or symbols</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Display Name
                  </label>
                  <input
                    type="text"
                    name="seller_display_name"
                    value={formData.seller_display_name}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="John Seller (optional)"
                  />
                </div>
              </div>
            </div>

            {/* Images */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Images</h3>

              {/* Image Mode Tabs */}
              <div className="mb-4">
                <div className="border-b border-gray-200">
                  <nav className="-mb-px flex space-x-8">
                    <button
                      type="button"
                      onClick={() => setImageMode('upload')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        imageMode === 'upload'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      Upload Files
                    </button>
                    <button
                      type="button"
                      onClick={() => setImageMode('url')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        imageMode === 'url'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      Enter URLs
                    </button>
                  </nav>
                </div>
              </div>

              {/* Upload Mode */}
              {imageMode === 'upload' && (
                <div className="space-y-4">
                  <FileUpload
                    onFilesChange={handleFilesChange}
                    maxFiles={10}
                    disabled={isLoading}
                    existingFiles={uploadedFiles}
                  />

                  {uploadedFiles.length > 0 && (
                    <div className="text-sm text-gray-600">
                      <p>
                        {uploadedFiles.filter(f => f.status === 'success').length} of {uploadedFiles.length} files uploaded successfully
                      </p>
                      {uploadedFiles.some(f => f.status === 'error') && (
                        <p className="text-red-600 mt-1">
                          Some files failed to upload. Please remove them or try again.
                        </p>
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* URL Mode */}
              {imageMode === 'url' && (
                <div className="space-y-3">
                  {imageUrls.map((url, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <PhotoIcon className="h-5 w-5 text-gray-400" />
                      <input
                        type="url"
                        value={url}
                        onChange={(e) => handleImageUrlChange(index, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="https://example.com/image.jpg"
                      />
                      {imageUrls.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeImageUrlField(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <XMarkIcon className="h-5 w-5" />
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={addImageUrlField}
                    className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    + Add another image URL
                  </button>
                </div>
              )}
            </div>

            {/* Error/Success Messages */}
            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="text-sm text-red-700">{error}</div>
              </div>
            )}

            {success && (
              <div className="rounded-md bg-green-50 p-4">
                <div className="text-sm text-green-700">{success}</div>
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => router.push('/admin/dashboard')}
                className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading || isUploading || uploadedFiles.some(f => f.status === 'uploading')}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Adding Generator...' :
                 isUploading ? 'Uploading Files...' :
                 uploadedFiles.some(f => f.status === 'uploading') ? 'Files Uploading...' :
                 'Add Generator'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
