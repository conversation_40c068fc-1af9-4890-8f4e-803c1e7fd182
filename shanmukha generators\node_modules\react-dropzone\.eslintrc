{
  "parser": "@babel/eslint-parser",
  "parserOptions": {
    "ecmaVersion": 2017,
    "sourceType": "module"
  },
  "env": {
    "es6": true,
    "node": true,
    "jest": true
  },
  "plugins": [
    "import",
    "node",
    "prettier"
  ],
  "extends": [
    "eslint:recommended",
    "plugin:prettier/recommended"
  ],
  "rules": {
    "strict": 0,
    "node/no-unpublished-require": 0,
    // Import
    "import/no-extraneous-dependencies": [
      2,
      {
        "devDependencies": [
          "webpack*.js",
          "**/*.spec.js",
          "**/*.config.js",
          "**/testSetup.js"
        ]
      }
    ]
  }
}
