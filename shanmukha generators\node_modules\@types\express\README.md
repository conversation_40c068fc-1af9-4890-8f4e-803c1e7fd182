# Installation
> `npm install --save @types/express`

# Summary
This package contains type definitions for express (http://expressjs.com).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express.

### Additional Details
 * Last updated: Sat, 07 Jun 2025 02:15:25 GMT
 * Dependencies: [@types/body-parser](https://npmjs.com/package/@types/body-parser), [@types/express-serve-static-core](https://npmjs.com/package/@types/express-serve-static-core), [@types/serve-static](https://npmjs.com/package/@types/serve-static)

# Credits
These definitions were written by [<PERSON>](https://github.com/b<PERSON><PERSON><PERSON>), [Puneet Arora](https://github.com/puneetar), [<PERSON>](https://github.com/dfrankland), and [<PERSON>](https://github.com/bjohansebas).
