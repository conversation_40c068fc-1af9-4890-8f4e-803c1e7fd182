{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/shanmukha%20generators/src/app/api/admin/auth/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\n\nconst ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'admin123';\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { password } = body;\n\n    if (!password) {\n      return NextResponse.json(\n        {\n          success: false,\n          error: 'Password is required'\n        },\n        { status: 400 }\n      );\n    }\n\n    // In a real application, you would hash the admin password and store it securely\n    // For this demo, we're doing a simple comparison\n    if (password !== ADMIN_PASSWORD) {\n      return NextResponse.json(\n        {\n          success: false,\n          error: 'Invalid password'\n        },\n        { status: 401 }\n      );\n    }\n\n    // Generate JWT token\n    const token = jwt.sign(\n      { \n        role: 'admin',\n        timestamp: Date.now()\n      },\n      JWT_SECRET,\n      { expiresIn: '24h' }\n    );\n\n    // Set HTTP-only cookie\n    const response = NextResponse.json({\n      success: true,\n      message: 'Authentication successful',\n      data: {\n        role: 'admin',\n        expiresIn: '24h'\n      }\n    });\n\n    response.cookies.set('admin-token', token, {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === 'production',\n      sameSite: 'strict',\n      maxAge: 24 * 60 * 60 * 1000 // 24 hours\n    });\n\n    return response;\n\n  } catch (error) {\n    console.error('Error in admin authentication:', error);\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Authentication failed',\n        message: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const token = request.cookies.get('admin-token')?.value;\n\n    if (!token) {\n      return NextResponse.json(\n        {\n          success: false,\n          error: 'No authentication token found'\n        },\n        { status: 401 }\n      );\n    }\n\n    try {\n      const decoded = jwt.verify(token, JWT_SECRET) as any;\n      \n      return NextResponse.json({\n        success: true,\n        data: {\n          role: decoded.role,\n          authenticated: true\n        }\n      });\n\n    } catch (jwtError) {\n      return NextResponse.json(\n        {\n          success: false,\n          error: 'Invalid or expired token'\n        },\n        { status: 401 }\n      );\n    }\n\n  } catch (error) {\n    console.error('Error verifying admin authentication:', error);\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Authentication verification failed',\n        message: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function DELETE(request: NextRequest) {\n  try {\n    const response = NextResponse.json({\n      success: true,\n      message: 'Logged out successfully'\n    });\n\n    // Clear the authentication cookie\n    response.cookies.set('admin-token', '', {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === 'production',\n      sameSite: 'strict',\n      maxAge: 0\n    });\n\n    return response;\n\n  } catch (error) {\n    console.error('Error in admin logout:', error);\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Logout failed',\n        message: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;AAEA,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AACrD,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAEtC,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,QAAQ,EAAE,GAAG;QAErB,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,iFAAiF;QACjF,iDAAiD;QACjD,IAAI,aAAa,gBAAgB;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,QAAQ,uIAAA,CAAA,UAAG,CAAC,IAAI,CACpB;YACE,MAAM;YACN,WAAW,KAAK,GAAG;QACrB,GACA,YACA;YAAE,WAAW;QAAM;QAGrB,uBAAuB;QACvB,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACjC,SAAS;YACT,SAAS;YACT,MAAM;gBACJ,MAAM;gBACN,WAAW;YACb;QACF;QAEA,SAAS,OAAO,CAAC,GAAG,CAAC,eAAe,OAAO;YACzC,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ,KAAK,KAAK,KAAK,KAAK,WAAW;QACzC;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,gBAAgB;QAElD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI;YACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;YAElC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;oBACJ,MAAM,QAAQ,IAAI;oBAClB,eAAe;gBACjB;YACF;QAEF,EAAE,OAAO,UAAU;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACjC,SAAS;YACT,SAAS;QACX;QAEA,kCAAkC;QAClC,SAAS,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI;YACtC,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ;QACV;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}