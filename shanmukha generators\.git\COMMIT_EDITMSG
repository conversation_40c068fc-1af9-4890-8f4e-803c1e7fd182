Fix file upload functionality: implement automatic upload on file drop

- Add automatic file upload when files are dropped in FileUpload component
- Remove manual upload logic from add-generator page that was never triggered
- Add retry functionality for failed uploads with UI button
- Improve error handling and validation for upload states
- Add better logging to upload API route for debugging
- Fix 'Please wait for all file uploads to complete' error by ensuring uploads start immediately
