# Installation
> `npm install --save @types/jsonwebtoken`

# Summary
This package contains type definitions for jsonwebtoken (https://github.com/auth0/node-jsonwebtoken).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsonwebtoken.

### Additional Details
 * Last updated: Mon, 16 Jun 2025 07:35:37 GMT
 * Dependencies: [@types/ms](https://npmjs.com/package/@types/ms), [@types/node](https://npmjs.com/package/@types/node)

# Credits
These definitions were written by [<PERSON><PERSON> LUCE](https://github.com/SomaticIT), [<PERSON>](https://github.com/daniel<PERSON>), [<PERSON><PERSON> BERNARD](https://github.com/brikou), [<PERSON>eli-P<PERSON><PERSON>stilä](https://github.com/vpk), [<PERSON>](https://github.com/GeneralistDev), [<PERSON><PERSON><PERSON>](https://github.com/kettil), [<PERSON>](https://github.com/RunAge), [<PERSON>lai<PERSON>](https://github.com/nflaig), [Linus Unnebäck](https://github.com/LinusU), [Ivan Sieder](https://github.com/ivansieder), [Piotr Błażejewicz](https://github.com/peterblazejewicz), and [Nandor Kraszlan](https://github.com/nandi95).
