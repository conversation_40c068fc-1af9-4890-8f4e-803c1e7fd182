module.exports = {

"[project]/.next-internal/server/app/api/admin/generators/manual/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/shanmukha-generators';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[project]/src/models/Generator.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const GeneratorSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema({
    // Core generator information
    brand: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50
    },
    model: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    price: {
        type: Number,
        required: true,
        min: 0
    },
    hours_run: {
        type: Number,
        required: true,
        min: 0
    },
    location_text: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200
    },
    description: {
        type: String,
        required: true,
        trim: true,
        maxlength: 1000
    },
    // Media files
    images: [
        {
            url: {
                type: String,
                required: true
            },
            filename: String,
            size: Number,
            mimetype: String
        }
    ],
    // Status management
    status: {
        type: String,
        enum: [
            'pending_review',
            'for_sale',
            'sold',
            'rejected',
            'failed_parsing'
        ],
        default: 'pending_review',
        required: true
    },
    // Relationships
    seller_id: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    // Audit trail
    audit_trail: {
        whatsapp_message_id: {
            type: String,
            required: true
        },
        original_message_text: String,
        parsed_at: {
            type: Date,
            default: Date.now
        },
        parsing_errors: [
            String
        ],
        approved_by: {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema.Types.ObjectId,
            ref: 'User'
        },
        approved_at: Date,
        rejected_reason: String
    },
    // Auto-generated tags for searching
    tags: [
        {
            type: String,
            lowercase: true,
            trim: true
        }
    ],
    // Additional metadata
    views: {
        type: Number,
        default: 0
    },
    whatsapp_clicks: {
        type: Number,
        default: 0
    },
    // Sold information
    sold_date: Date,
    sold_price: Number
}, {
    timestamps: true,
    collection: 'generators'
});
// Indexes
GeneratorSchema.index({
    status: 1
});
GeneratorSchema.index({
    seller_id: 1
});
GeneratorSchema.index({
    brand: 1
});
GeneratorSchema.index({
    price: 1
});
GeneratorSchema.index({
    hours_run: 1
});
GeneratorSchema.index({
    tags: 1
});
GeneratorSchema.index({
    createdAt: -1
});
GeneratorSchema.index({
    'audit_trail.whatsapp_message_id': 1
});
// Text index for search
GeneratorSchema.index({
    brand: 'text',
    model: 'text',
    description: 'text',
    location_text: 'text',
    tags: 'text'
});
// Pre-save middleware to generate tags
GeneratorSchema.pre('save', function(next) {
    if (this.isModified('brand') || this.isModified('model') || this.isModified('location_text')) {
        const tags = new Set();
        if (this.brand) {
            this.brand.toLowerCase().split(/\s+/).forEach((word)=>{
                if (word.length > 2) tags.add(word);
            });
        }
        if (this.model) {
            this.model.toLowerCase().split(/\s+/).forEach((word)=>{
                if (word.length > 2) tags.add(word);
            });
        }
        if (this.location_text) {
            this.location_text.toLowerCase().split(/\s+/).forEach((word)=>{
                if (word.length > 2) tags.add(word);
            });
        }
        this.tags = Array.from(tags);
    }
    next();
});
// Virtual for formatted price
GeneratorSchema.virtual('formattedPrice').get(function() {
    return `₹${this.price.toLocaleString('en-IN')}`;
});
// Virtual for age calculation
GeneratorSchema.virtual('listingAge').get(function() {
    const now = new Date();
    const created = this.createdAt;
    const diffTime = Math.abs(now.getTime() - created.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return `${Math.floor(diffDays / 30)} months ago`;
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Generator || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Generator', GeneratorSchema);
}}),
"[project]/src/models/User.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const UserSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema({
    whatsapp_id: {
        type: String,
        required: true,
        unique: true,
        trim: true,
        match: [
            /^\d{10,15}$/,
            'Please enter a valid WhatsApp ID'
        ]
    },
    display_name: {
        type: String,
        trim: true,
        maxlength: 100
    },
    role: {
        type: String,
        enum: [
            'seller',
            'admin'
        ],
        default: 'seller'
    },
    is_active: {
        type: Boolean,
        default: true
    },
    total_listings: {
        type: Number,
        default: 0
    },
    successful_sales: {
        type: Number,
        default: 0
    },
    first_message_date: {
        type: Date,
        default: Date.now
    },
    last_activity: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true,
    collection: 'users'
});
// Indexes
UserSchema.index({
    whatsapp_id: 1
});
UserSchema.index({
    role: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.User || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('User', UserSchema);
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/app/api/admin/generators/manual/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Generator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Generator.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
;
;
;
;
;
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
// Verify admin authentication
async function verifyAdmin(request) {
    try {
        const token = request.cookies.get('admin-token')?.value;
        if (!token) {
            return false;
        }
        const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, JWT_SECRET);
        return decoded.role === 'admin';
    } catch (error) {
        return false;
    }
}
async function POST(request) {
    try {
        // Verify admin authentication
        const isAdmin = await verifyAdmin(request);
        if (!isAdmin) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Unauthorized access'
            }, {
                status: 401
            });
        }
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const body = await request.json();
        const { brand, model, price, hours_run, location_text, description, seller_whatsapp_id, seller_display_name, images = [] } = body;
        // Validate required fields
        if (!brand || !model || !price || hours_run === undefined || !location_text || !description || !seller_whatsapp_id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Missing required fields'
            }, {
                status: 400
            });
        }
        // Validate data types
        if (typeof price !== 'number' || price <= 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Price must be a positive number'
            }, {
                status: 400
            });
        }
        if (typeof hours_run !== 'number' || hours_run < 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Hours run must be a non-negative number'
            }, {
                status: 400
            });
        }
        // Validate WhatsApp ID format
        if (!/^\d{10,15}$/.test(seller_whatsapp_id)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Invalid WhatsApp ID format'
            }, {
                status: 400
            });
        }
        // Find or create seller
        let seller = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            whatsapp_id: seller_whatsapp_id
        });
        if (!seller) {
            seller = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
                whatsapp_id: seller_whatsapp_id,
                display_name: seller_display_name || `Seller ${seller_whatsapp_id.slice(-4)}`,
                role: 'seller'
            });
            await seller.save();
        } else if (seller_display_name && !seller.display_name) {
            seller.display_name = seller_display_name;
            await seller.save();
        }
        // Process images
        const processedImages = images.map((img, index)=>{
            if (typeof img === 'string') {
                return {
                    url: img,
                    filename: `manual_upload_${Date.now()}_${index}`,
                    size: 0,
                    mimetype: 'image/jpeg'
                };
            } else if (img && img.url) {
                return {
                    url: img.url,
                    filename: img.filename || `manual_upload_${Date.now()}_${index}`,
                    size: img.size || 0,
                    mimetype: img.mimetype || 'image/jpeg'
                };
            }
            return null;
        }).filter(Boolean);
        // Create generator
        const generator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Generator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
            brand: brand.trim(),
            model: model.trim(),
            price: price,
            hours_run: hours_run,
            location_text: location_text.trim(),
            description: description.trim(),
            images: processedImages,
            seller_id: seller._id,
            status: 'for_sale',
            audit_trail: {
                whatsapp_message_id: `manual_${Date.now()}_${Math.random().toString(36).substring(7)}`,
                original_message_text: `Manually added by admin: ${brand} ${model}`,
                parsed_at: new Date(),
                parsing_errors: [],
                approved_by: seller._id,
                approved_at: new Date()
            }
        });
        await generator.save();
        // Update seller's listing count
        seller.total_listings += 1;
        await seller.save();
        // Populate seller information for response
        await generator.populate('seller_id', 'whatsapp_id display_name total_listings successful_sales');
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                generator,
                message: 'Generator added successfully'
            }
        });
    } catch (error) {
        console.error('Error creating manual generator:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to create generator',
            message: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__6c6209b2._.js.map